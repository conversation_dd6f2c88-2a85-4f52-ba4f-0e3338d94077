# Review Gate V2 - Cursor AI Rules Configuration

## Core Principles
- Always prioritize code quality and maintainability
- Provide constructive feedback with specific suggestions
- Focus on security, performance, and best practices
- Encourage clean, readable, and well-documented code

## Code Review Guidelines

### 1. Security Review
- Check for potential security vulnerabilities
- Validate input sanitization and validation
- Review authentication and authorization mechanisms
- Identify potential injection attacks (SQL, XSS, etc.)
- Ensure sensitive data is properly handled

### 2. Performance Optimization
- Identify inefficient algorithms or data structures
- Check for unnecessary loops or redundant operations
- Review database queries for optimization opportunities
- Suggest caching strategies where appropriate
- Monitor memory usage and potential leaks

### 3. Code Quality Standards
- Enforce consistent naming conventions
- Ensure proper error handling and logging
- Check for code duplication and suggest refactoring
- Validate proper use of design patterns
- Review function/method complexity and suggest simplification

### 4. Documentation Requirements
- Ensure all public APIs are documented
- Check for meaningful comments explaining complex logic
- Validate README files and setup instructions
- Review inline documentation for accuracy

### 5. Testing Standards
- Verify adequate test coverage
- Check for both unit and integration tests
- Ensure tests are meaningful and not just for coverage
- Validate test data and mock usage

## Review Gate V2 Integration Rules

### When to Trigger Review Gate
- Before committing significant changes
- When implementing new features
- During refactoring sessions
- Before merging pull requests
- When uncertain about implementation approach

### Review Process
1. **Initial Assessment**: Quick overview of changes
2. **Detailed Analysis**: Line-by-line review
3. **Feedback Generation**: Constructive suggestions
4. **Action Items**: Clear next steps
5. **Follow-up**: Verification of improvements

### Communication Style
- Be specific and actionable in feedback
- Provide examples when suggesting improvements
- Explain the reasoning behind recommendations
- Maintain a collaborative and supportive tone
- Prioritize issues by severity (critical, major, minor)

## MCP Integration Guidelines

### Tool Usage
- Use `review_gate_chat` for interactive feedback sessions
- Leverage popup dialogs for urgent reviews
- Implement timeout handling for user responses
- Support file attachments and visual feedback

### Response Handling
- Process user input with appropriate context
- Handle timeouts gracefully
- Provide clear status updates
- Support both text and multimedia responses

## Language-Specific Rules

### Python
- Follow PEP 8 style guidelines
- Use type hints for better code clarity
- Implement proper exception handling
- Prefer list comprehensions over loops where appropriate

### JavaScript/TypeScript
- Use modern ES6+ features
- Implement proper async/await patterns
- Follow consistent naming conventions
- Use TypeScript for better type safety

### General Best Practices
- Keep functions small and focused
- Use meaningful variable and function names
- Implement proper separation of concerns
- Follow DRY (Don't Repeat Yourself) principle

## Integration Commands

### Cursor Settings Integration
To integrate these rules into Cursor:
1. Open Cursor Settings (Cmd/Ctrl + ,)
2. Navigate to "AI Rules" section
3. Paste this configuration
4. Save and restart Cursor

### MCP Server Configuration
Ensure the Review Gate V2 MCP server is properly configured:
- Python 3.8+ required
- Install dependencies: `pip install -r requirements_simple.txt`
- Configure MCP client settings in Cursor
- Verify server connectivity and tool availability

## Troubleshooting

### Common Issues
- MCP server connection failures
- Timeout issues with user input
- File permission problems
- Extension compatibility issues

### Debug Steps
1. Check MCP server logs
2. Verify file permissions
3. Test trigger file creation
4. Validate JSON response format
5. Monitor extension status

## Version Information
- Review Gate V2 - Advanced MCP Server
- Compatible with Cursor IDE
- Supports speech-to-text integration
- Multi-platform support (Windows, macOS, Linux)

---
*This configuration enables comprehensive code review capabilities through the Review Gate V2 system integrated with Cursor's AI features.*
